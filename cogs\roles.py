# Responsible for handling the role system

import discord
import asyncio
import json
import os
from discord.ext import commands
from discord import app_commands
from .db import db

class Roles(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.EMBED_COLOR = 0x566374
        
        # Initialize role messages from database
        self.role_messages = {}
        
        # Add single selection categories
        self.single_select_categories = [
            "Color Customization",
            "Gender",
            "Age",
            "Fursona Types"
        ]

        # Role configurations (updating Gender category and keeping all other categories the same)
        self.role_categories = {
            "hello": {
                "title": "Notification Pings:",
                "description": "",
                "roles": {
                    "<:announcement:1378881106820075520>": {
                        "name": "Announcements",
                        "description": "-# Get notified when announcements are made."
                    },
                    "<:Welcome:1378880033354940527>": {
                        "name": "Greetings Crew",
                        "description": "-# Get notified when new members are verified."
                    },
                    "<:wolf_Yay:1349582406415548488>": {
                        "name": "Birthday Alert",
                        "description": "-# Get notified when it is someone's birthday.",
                        "role_name": "Birthday Alert"
                    },
                    "<:pray1:1378880031614435428>": {
                        "name": "Prayer Emergencies",
                        "description": "-# Get notified when there is urgent prayers needed."
                    },
                    "<:scroll1:1378880027613200486>": {
                        "name": "Daily Bible Verse",
                        "description": "-# Get notified when the daily Bible verse is posted."
                    },
                    "<:Bible:1378880036798599259>": {
                        "name": "Bible Study Event",
                        "description": "-# Get notified when our Bible studies occur."
                    },
                    "<:study:1381456077794836550>": {
                        "name": "Worship Event",
                        "description": "-# Get notified when the worship event begins.",
                        "role_name": "Worship Event"
                    }
                }
            },
            "Channel Customization": {
                "title": "Channel Customization:",
                "description": "",
                "roles": {
                    "<:Support:1378880415535992994>": {
                        "name": "Show **Supportive-Chat** Channel",
                        "description": "-# Gives access to view the \"Supportive-Chat\" channel.",
                        "role_name": "Supportive Chat"
                    },
                    "<:Lock:1378880413732442213>": {
                        "name": "Show **Confession-Chat** Channel",
                        "description": "-# Gives access to view the \"Confession-Chat\" channel.",
                        "role_name": "Confession Chat"
                    },
                    "<:Hug:1378880414705520662>": {
                        "name": "Show **Affection-Hangout** Channel",
                        "description": "-# Gives access to view the \"Affection-Hangout\" channel.",
                        "role_name": "Affection Hangout"
                    },
                    "<:Question:1378885412436574278>": {
                        "name": "Show **Question-Mayhem** Channel",
                        "description": "-# Gives access to view the \"Question-Mayhem\" channel.",
                        "role_name": "Question Mayhem"
                    }
                }
            },
            "Color Customization": {
                "title": "Color Customization:",
                "description": "",
                "roles": {
                    "<:Red:1378880751755464845>": "Red",    
                    "<:Coral:1378880711259590686>": "Coral",           
                    "<:Orange:1378880778007740560>": "Orange",       
                    "<:Yellow:1378880799146905681>": "Yellow", 
                    "<:Mint:1378880754393813153>": "Mint",  
                    "<:Green:1378880713322926201>": "Green",
                    "<:Emerald:1378880712479998042>": "Emerald",
                    "<:Aqua:1378880798228353074>": "Aqua",
                    "<:Turquoise:1378880708826632274>": "Turquoise",
                    "<:Blue:1378880710160420874>": "Blue",
                    "<:Lavender:1378880707107225730>": "Lavender",
                    "<:Purple:1378880776908574871>": "Purple",
                    "<:Mauve:1378880752586063923>": "Mauve",
                    "<:Pink:1378880755249184970>": "Pink"
                }
            },
            "Gender": {
                "title": "Gender:",
                "description": "",
                "roles": {
                    "<:Male:1378880452080832574>": {
                        "name": "Male [ He/Him ]",
                        "role_name": "Male"
                    },
                    "<:Female:1378880452974088402>": {
                        "name": "Female [ She/Her ]",
                        "role_name": "Female"
                    }
                }
            },
            "Age": {
                "title": "Age:",
                "description": "",
                "roles": {
                    "<:1:1378880453834047639>": "13 - 17",  
                    "<:2:1378880454626771047>": "18 - 25",         
                    "<:3:1378880456300298300>": "26 - 35",        
                    "<:4:1378880458145796157>": "36 - 45", 
                    "<:5_:1378880459081252985>": "46+"      
                }
            },
            "Fursona Types": {
                "title": "Fursona Types:",
                "description": "",
                "roles": {
                    "<:ClassicFur:1378880860476145668>": "Classic Fur",   
                    "<:ReptillianFur:1378880844659163357>": "Reptilian Fur",   
                    "<:CyberneticFur:1378880880713400441>": "Cybernetic Fur", 
                    "<:AvianFur:1378880862258724904>": "Avian Fur",
                    "<:AquaticFur:1378880881623564370>": "Aquatic Fur",
                    "<:ArthropodFur:1378880843661180940>": "Arthropod Fur",
                    "<:MythicalFur:1378880842759143565>": "Mythical Fur",
                    "<:ElementalFur:1378880878251343983>": "Elemental Fur",
                    "<:HybridFur:1378880861440704703>": "Hybrid Fur"           
                }
            },
            "Miscellaneous": {
                "title": "Miscellaneous:",
                "description": "",
                "roles": {
                    "<:Medal:1378881064545681559>": "Veteran",         
                    "<:rpg:1378881034388635778>": "Weapon Enthusiast",  
                    "<:Science:1378881111928733786>": "Scientist",      
                    "<:Tech:1378881113065390174>": "Tech Enthusiast",
                    "<:History:1378881108636074136>": "Historian",
                    "<:Space:1378881110053879840>": "Space Enthusiast",
                    "<:Artist:1378881032714977351>": "Artist",
                    "<:Music:1378881060368154714>": "Music Enthusiast",
                    "<:Writer:1378881030492262482>": "Writer",
                    "<:Record:1378881059315515442>": "Content Creator",
                    "<:Car:1378881065812361276>": "Car Enthusiast",
                    "<:meme:1378881029087039621>": "Meme Enjoyer",
                    "<:Joystick:1378881035378491475>": "Casual Gamer",
                    "<:Fursuiter:1378881062611976262>": "Fursuiter",
                    "<:Fitness:1378881107491160195>": "Fitness Enthusiast",
                    "<:Garden:1378881061223792641>": "Gardener",
                    "<:Cooking:1378881031456821378>": "Home Cook"
                }
            }
        }

    def create_role_embed(self, category_data):
        """Creates an embed for a role category"""
        embed = discord.Embed(
            title=category_data["title"],
            description=category_data["description"],
            color=self.EMBED_COLOR
        )
        
        # Build the roles text with proper formatting and spacing
        roles_text = ""
        for emoji, role_data in category_data["roles"].items():
            if isinstance(role_data, dict):
                # Handle dictionary format
                roles_text += f"{emoji}︱{role_data['name']}\n"
                if 'description' in role_data:
                    roles_text += f"{role_data['description']}\n"
            else:
                # Handle string format
                roles_text += f"{emoji}︱{role_data}\n"
            roles_text += "\n"
        
        if roles_text:
            embed.description = roles_text.strip()
        
        return embed

    async def load_role_messages(self):
        """Load role messages data from database"""
        try:
            self.role_messages = await db.get_all_role_messages()
            print(f"✅ Loaded {len(self.role_messages)} role messages from database")
        except Exception as e:
            print(f"❌ Error loading role messages from database: {e}")
            self.role_messages = {}

    async def save_role_message(self, message_id, channel_id, role_data):
        """Save individual role message data to database"""
        try:
            # Update local cache with proper structure: channel_id -> message_id -> category_name
            channel_id_str = str(channel_id)
            message_id_str = str(message_id)
            
            if channel_id_str not in self.role_messages:
                self.role_messages[channel_id_str] = {}
            
            # role_data should contain {message_id: category_name}
            if message_id_str in role_data:
                self.role_messages[channel_id_str][message_id_str] = role_data[message_id_str]
            
            # Save to database
            await db.save_role_message(message_id, channel_id, role_data)
            print(f"✅ Saved role message {message_id} to database")
        except Exception as e:
            print(f"❌ Error saving role message to database: {e}")

    async def initialize_role_reactions(self):
        """Verify messages exist after bot restart without modifying reactions"""
        channels_to_remove = []
        messages_to_remove = {}

        # First pass: collect all invalid entries
        for channel_id, message_data in self.role_messages.items():
            channel = self.bot.get_channel(int(channel_id))
            if not channel:
                channels_to_remove.append(channel_id)
                continue

            messages_to_remove[channel_id] = []
            for msg_id in message_data.keys():
                try:
                    await channel.fetch_message(int(msg_id))
                except Exception as e:
                    messages_to_remove[channel_id].append(msg_id)

        # Second pass: remove invalid entries
        for channel_id in channels_to_remove:
            del self.role_messages[channel_id]

        for channel_id, msg_ids in messages_to_remove.items():
            for msg_id in msg_ids:
                if channel_id in self.role_messages and msg_id in self.role_messages[channel_id]:
                    del self.role_messages[channel_id][msg_id]
                    # Delete from database
                    try:
                        await db.delete_role_message(int(msg_id))
                    except Exception as e:
                        print(f"❌ Error deleting role message {msg_id} from database: {e}")
            # Remove channel if it has no messages
            if channel_id in self.role_messages and not self.role_messages[channel_id]:
                del self.role_messages[channel_id]

        if messages_to_remove:
            print(f"✅ Cleaned up {sum(len(msgs) for msgs in messages_to_remove.values())} invalid role messages from database")

    @commands.Cog.listener()
    async def on_ready(self):
        """Initialize role reactions when bot starts"""
        # Load role messages from database first
        await self.load_role_messages()
        await self.initialize_role_reactions()

    @app_commands.command(
        name="rolechannelsetup",
        description="Setup the role selection channel"
    )
    @app_commands.default_permissions(administrator=True)
    async def rolechannelsetup(self, interaction: discord.Interaction):
        if not interaction.channel.permissions_for(interaction.guild.me).send_messages:
            await interaction.response.send_message(
                "I don't have permission to send messages in this channel!",
                ephemeral=True
            )
            return

        if not interaction.channel.permissions_for(interaction.guild.me).embed_links:
            await interaction.response.send_message(
                "I don't have permission to send embeds in this channel!",
                ephemeral=True
            )
            return

        await interaction.response.defer(ephemeral=True)

        try:
            # Delete old messages from role_messages.json
            old_channels = list(self.role_messages.keys())
            for channel_id in old_channels:
                try:
                    channel = self.bot.get_channel(int(channel_id))
                    if channel:
                        # Delete old messages
                        old_messages = list(self.role_messages[channel_id].keys())
                        for msg_id in old_messages:
                            try:
                                old_message = await channel.fetch_message(int(msg_id))
                                await old_message.delete()
                            except:
                                pass
                except:
                    pass

            # Clear role_messages from database and local cache
            try:
                self.role_messages = {}
                await db.clear_all_role_messages()
                print("✅ Cleared all role messages from database")
            except Exception as e:
                print(f"❌ Error clearing role messages from database: {e}")
                self.role_messages = {}

            # Clear current channel if bot has manage_messages permission
            if interaction.channel.permissions_for(interaction.guild.me).manage_messages:
                await interaction.channel.purge(limit=None)

            # Initialize new message data for this channel
            channel_id = str(interaction.channel.id)
            self.role_messages[channel_id] = {}

            # Send first embed immediately
            first_category = True
            for category_name, category_data in self.role_categories.items():
                embed = self.create_role_embed(category_data)
                
                if first_category:
                    message = await interaction.channel.send(embed=embed)
                    first_category = False
                else:
                    await asyncio.sleep(420)  # 7 minutes
                    message = await interaction.channel.send(embed=embed)
                
                # Store message ID and category name in database
                role_data = {str(message.id): category_name}
                await self.save_role_message(message.id, interaction.channel.id, role_data)

                # Add reactions
                for emoji_id in category_data["roles"].keys():
                    await message.add_reaction(emoji_id)

            await interaction.followup.send(
                "Role setup has been completed.",
                ephemeral=True
            )

        except Exception as e:
            await interaction.followup.send(
                f"An error occurred while setting up: {str(e)}",
                ephemeral=True
            )

    @commands.Cog.listener()
    async def on_raw_reaction_add(self, payload):
        """Handle role assignment when a reaction is added"""
        if payload.user_id == self.bot.user.id:
            return

        channel_id = str(payload.channel_id)
        message_id = str(payload.message_id)

        # Check if this message is part of our role system (early return to avoid spam)
        if (channel_id not in self.role_messages or 
            message_id not in self.role_messages[channel_id]):
            return

        category_name = self.role_messages[channel_id][message_id]
        category_data = self.role_categories[category_name]

        print(f"✅ Found role category: {category_name}")

        # Find the role name for this reaction
        emoji_str = str(payload.emoji)
        
        if emoji_str not in category_data["roles"]:
            print(f"❌ Emoji {emoji_str} not found in category roles")
            return

        guild = self.bot.get_guild(payload.guild_id)
        if not guild:
            return

        member = guild.get_member(payload.user_id)
        if not member:
            return

        # Get the role data and name
        role_data = category_data["roles"][emoji_str]
        if isinstance(role_data, dict):
            role_name = role_data.get("role_name", role_data["name"])
        else:
            role_name = role_data

        # Get the role to add
        role = discord.utils.get(guild.roles, name=role_name)
        if not role:
            print(f"❌ Role '{role_name}' not found in guild")
            return

        # For single select categories, check if they already have a role from this category
        if category_name in self.single_select_categories:
            # Check if user already has any role from this category
            for emoji_key, role_info in category_data["roles"].items():
                if isinstance(role_info, dict):
                    existing_role_name = role_info.get("role_name", role_info["name"])
                else:
                    existing_role_name = role_info
                
                existing_role = discord.utils.get(guild.roles, name=existing_role_name)
                if existing_role and existing_role in member.roles:
                    # User already has a role from this category, prevent selection
                    try:
                        # Remove their new reaction
                        message = await self.bot.get_channel(payload.channel_id).fetch_message(payload.message_id)
                        await message.remove_reaction(payload.emoji, member)
                        
                        # Send them a DM
                        await member.send("You can only select 1 role for that embed! Please unselect your current role first.")
                        print(f"⚠️ {member.display_name} tried to select multiple roles in {category_name} category")
                        return
                    except (discord.Forbidden, discord.NotFound):
                        print(f"❌ Could not remove reaction or send DM to {member.display_name}")
                        return

        try:
            # For single select categories, add the role (we've already checked they don't have another)
            # For other categories, toggle the role
            if category_name in self.single_select_categories:
                print(f"🔄 Adding role {role.name} to {member.display_name} (single select)")
                await member.add_roles(role)
                print(f"✅ Successfully added role {role.name}")
            else:
                # Toggle the role for multi-select categories
                if role in member.roles:
                    print(f"🔄 Removing role {role.name} from {member.display_name} (toggle)")
                    await member.remove_roles(role)
                    print(f"✅ Successfully removed role {role.name}")
                else:
                    print(f"🔄 Adding role {role.name} to {member.display_name} (toggle)")
                    await member.add_roles(role)
                    print(f"✅ Successfully added role {role.name}")
        except discord.Forbidden:
            print(f"❌ Forbidden: Bot lacks permission to manage roles")
        except Exception as e:
            print(f"❌ Error managing role: {e}")

    @commands.Cog.listener()
    async def on_raw_reaction_remove(self, payload):
        """Handle role removal when a reaction is removed"""
        if payload.user_id == self.bot.user.id:
            return

        channel_id = str(payload.channel_id)
        message_id = str(payload.message_id)

        # Check if this message is part of our role system (early return to avoid processing non-role reactions)
        if (channel_id not in self.role_messages or 
            message_id not in self.role_messages[channel_id]):
            return

        category_name = self.role_messages[channel_id][message_id]
        category_data = self.role_categories[category_name]

        # Find the role name for this reaction
        emoji_str = str(payload.emoji)
        if emoji_str not in category_data["roles"]:
            return

        role_data = category_data["roles"][emoji_str]
        # Handle both dictionary and string formats
        if isinstance(role_data, dict):
            role_name = role_data.get("role_name", role_data["name"])
        else:
            role_name = role_data

        guild = self.bot.get_guild(payload.guild_id)
        if not guild:
            return

        member = guild.get_member(payload.user_id)
        if not member:
            return

        role = discord.utils.get(guild.roles, name=role_name)
        if not role:
            return

        try:
            await member.remove_roles(role)
            print(f"✅ Removed role {role.name} from {member.display_name}")
        except discord.Forbidden:
            print(f"❌ Forbidden: Bot lacks permission to remove role {role.name}")
        except Exception as e:
            print(f"❌ Error removing role: {e}")

async def setup(bot):
    await bot.add_cog(Roles(bot))



