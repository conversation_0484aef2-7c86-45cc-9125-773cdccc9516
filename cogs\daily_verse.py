import discord
from discord.ext import commands
import asyncio
import aiohttp
from datetime import datetime, timedelta
import pytz
import html
import os
from dotenv import load_dotenv

class DailyVerse(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.BIBLE_GATEWAY_URL = 'https://www.biblegateway.com/votd/get/?format=json&version=ESV'
        self.CHANNEL_ID = 1341235184246198342  # Your channel ID
        self.ROLE_ID = 1349172304571138108     # Your role ID for pinging
        self.task = None
        
    async def fetch_verse_of_the_day(self):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.BIBLE_GATEWAY_URL) as response:
                    data = await response.json()
                    if 'votd' in data and 'text' in data['votd'] and 'reference' in data['votd']:
                        # Get the passage and clean it up
                        passage = html.unescape(data['votd']['text'].strip())
                        reference = html.unescape(data['votd']['reference'].strip())

                        # Remove extra spaces, line breaks, and fix formatting
                        passage = ' '.join(passage.split())
                        passage = passage.replace('—', ',')  # Replace em dash with comma
                        passage = passage.replace('  ', ' ')  # Remove double spaces
                        
                        # Capitalize first letter if needed
                        if passage and passage[0].islower():
                            passage = passage[0].upper() + passage[1:]

                        # Remove any existing quotes
                        passage = passage.strip('"')
                        
                        # Format the complete message with proper spacing (removed quotes)
                        formatted_passage = f'{passage}\n‒ {reference}『 ESV 』'
                        return formatted_passage
                    else:
                        return 'No passage found'
        except Exception as e:
            print(f'Error fetching verse: {e}')
            return 'Error fetching verse'

    async def send_daily_verse_ping(self):
        channel = self.bot.get_channel(self.CHANNEL_ID)
        if channel:
            try:
                verse = await self.fetch_verse_of_the_day()
                message = f'<@&{self.ROLE_ID}>\n{verse}'  # Removed extra ESV tag here
                await channel.send(message)
            except Exception as e:
                print(f'Error sending daily verse ping: {e}')

    async def daily_task(self):
        await self.bot.wait_until_ready()
        
        while not self.bot.is_closed():
            now = datetime.now(pytz.timezone('US/Eastern'))
            target_time = now.replace(hour=7, minute=0, second=0, microsecond=0)
            
            if now > target_time:
                target_time += timedelta(days=1)
            
            delta = target_time - now
            await asyncio.sleep(delta.total_seconds())
            
            await self.send_daily_verse_ping()

    @commands.Cog.listener()
    async def on_ready(self):
        if self.task is None:
            self.task = self.bot.loop.create_task(self.daily_task())

    def cog_unload(self):
        if self.task:
            self.task.cancel()

async def setup(bot):
    await bot.add_cog(DailyVerse(bot))








