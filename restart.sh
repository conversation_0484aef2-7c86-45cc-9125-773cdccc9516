#!/bin/bash
echo "Restarting bot in 3 seconds..."
sleep 3
cd "$(dirname "$0")"

# Try different Python executables in order of preference
if command -v python3 &> /dev/null; then
    echo "Using python3 command..."
    exec python3 main.py
elif command -v python &> /dev/null; then
    echo "Using python command..."
    exec python main.py
elif command -v py &> /dev/null; then
    echo "Using py command..."
    exec py main.py
else
    echo "ERROR: No Python executable found!"
    exit 1
fi