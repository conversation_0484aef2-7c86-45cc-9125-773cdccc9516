#!/usr/bin/env python3
"""
Test script to verify the warnings system works correctly
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cogs.db import db

async def test_warnings():
    """Test the warnings system"""
    try:
        print("Connecting to database...")
        await db.connect()
        print("Connected to database successfully!")
        
        # Test user ID
        test_user_id = 123456789
        test_reason = "Test warning for debugging"
        test_warned_by = 987654321
        test_warned_by_name = "TestModerator"
        
        print(f"\nTesting warning system for user {test_user_id}...")
        
        # Get initial warnings
        initial_warnings = await db.get_user_warnings(test_user_id)
        print(f"Initial warnings count: {len(initial_warnings)}")
        
        # Add a warning
        print(f"Adding warning: '{test_reason}'")
        await db.add_warning(test_user_id, test_reason, test_warned_by, test_warned_by_name)
        
        # Get warnings after adding
        after_warnings = await db.get_user_warnings(test_user_id)
        print(f"Warnings count after adding: {len(after_warnings)}")
        
        if len(after_warnings) > len(initial_warnings):
            print("SUCCESS: Warning added successfully!")
            print(f"Latest warning: {after_warnings[-1]}")
        else:
            print("ERROR: Warning was not added properly")
            
        # Test removing the warning
        if after_warnings:
            print(f"\nRemoving warning...")
            success = await db.remove_warning(test_user_id, len(after_warnings) - 1)
            if success:
                print("SUCCESS: Warning removed successfully!")
                final_warnings = await db.get_user_warnings(test_user_id)
                print(f"Final warnings count: {len(final_warnings)}")
            else:
                print("ERROR: Failed to remove warning")
        
        print("\nWarning system test completed!")
        
    except Exception as e:
        print(f"ERROR during test: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await db.disconnect()
        print("Disconnected from database")

if __name__ == "__main__":
    asyncio.run(test_warnings())