# Responsible for handling the clear, ping, restart, and avatar commands

import discord
from discord import app_commands
from discord.ext import commands
import os
import sys
import subprocess
import platform
from dotenv import load_dotenv


class GeneralCommands(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        load_dotenv()
        default_admin_roles = ['1378193717160312862', '1379995762016125048', '1338996580253958164']
        self.admin_roles = os.getenv('ADMIN_ROLES', ','.join(default_admin_roles)).split(',')
        self.EMBED_COLOR = 0x566374  # Add this for consistent embed colors
        
        # Debug: Print what admin roles were loaded
        print(f"DEBUG: GeneralCommands loaded admin roles: {self.admin_roles}")
        print(f"DEBUG: ADMIN_ROLES env var: {os.getenv('ADMIN_ROLES')}")
        print(f"DEBUG: Default admin roles: {default_admin_roles}")

    def has_admin_role(self, member: discord.Member) -> bool:
        # Debug: Print what we're checking
        print(f"DEBUG: Checking admin permissions for {member.display_name} (ID: {member.id})")
        print(f"DEBUG: Configured admin roles: {self.admin_roles}")
        print(f"DEBUG: User's roles: {[f'{role.name} (ID: {role.id})' for role in member.roles]}")
        print(f"DEBUG: User's role IDs as strings: {[str(role.id) for role in member.roles]}")
        
        # Check if any of the user's roles match admin roles
        result = any(str(role.id) in self.admin_roles for role in member.roles)
        print(f"DEBUG: Admin check result: {result}")
        return result

    @app_commands.command(name="help", description="Shows all available commands and their descriptions")
    async def help(self, interaction: discord.Interaction):
        embed = discord.Embed(
            title="Bot Information",
            color=self.EMBED_COLOR
        )

        # General Commands
        general = """
`/avatar`︲Show a enlarged version of the selected user's profile picture
`/ping`︲Check the bot's response time and latency
`/confess`︲Submit an anonymous confession"""
        embed.add_field(name="General Commands [ Public Commands ]:", value=general, inline=False)

        # Counting Commands
        counting = """
`/countinghighscore`︲Display the highest counting streak achieved
`/countingleaderboard`︲Show the top users ranked by counting performance
`/countingstatistics`︲View your personal counting statistics and performance"""
        embed.add_field(name="Counting Commands [ Public Commands ]:", value=counting, inline=False)

        # Moderation Commands
        moderation = """
`/rolechannelsetup`︲Setup the role selection channel
`/voicecalltimeout`︲Restrict a user from joining voice calls for a set duration
`/voicecalluntimeout`︲Lift a user's voice call timeout restriction
`/warn`︲Issues a warning to a user
`/warnremove`︲Removes a specific warning from a member
`/userwarnings`︲Displays all warnings issued to a user
`/clearwarnings`︲Removes all warnings from a member"""
        embed.add_field(name="Moderation Commands [ Private Commands ]:", value=moderation, inline=False)

        # Credits
        credits = """
Developer: <@1322931389057994806> & **Augment AI Assistant**

Testers: <@700692759102750770> & <@775897898394451998>

Fir Emojis: https://twitter.com/ThatLazyRat"""
        embed.add_field(name="Credit:", value=credits, inline=False)

        await interaction.response.send_message(embed=embed)

    @app_commands.command(name="ping", description="Check the bot's response time and latency")
    async def ping(self, interaction: discord.Interaction):
        latency = round(self.bot.latency * 1000)
        await interaction.response.send_message(f"Pong! [ {latency}ms ]")

    @app_commands.command(name="restart", description="Restart the bot")
    async def restart(self, interaction: discord.Interaction):
        if not self.has_admin_role(interaction.user):
            await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
            return

        await interaction.response.send_message("Restarting bot...", ephemeral=True)
        
        print("Bot restart requested by admin...")
        
        # Simple restart: just restart the current Python process
        try:
            # Close the bot gracefully
            await self.bot.close()
            
            # Restart the Python script
            os.execv(sys.executable, [sys.executable] + sys.argv)
            
        except Exception as e:
            print(f"Error during restart: {e}")
            try:
                await interaction.followup.send(f"Error during restart: {e}", ephemeral=True)
            except:
                pass

    @app_commands.command(name="clear", description="Remove a designated number of messages from the channel")
    @app_commands.describe(amount="Number of messages to delete (1-100)")
    async def clear(self, interaction: discord.Interaction, amount: int):
        if not self.has_admin_role(interaction.user):
            await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
            return

        if not 1 <= amount <= 100:
            await interaction.response.send_message(
                "Please specify a number between 1 and 100.", 
                ephemeral=True
            )
            return

        await interaction.response.defer(ephemeral=True)

        try:
            deleted = await interaction.channel.purge(limit=amount)
            await interaction.followup.send(
                f"Successfully deleted {len(deleted)} messages.", 
                ephemeral=True
            )
        except discord.Forbidden:
            await interaction.followup.send(
                "I don't have permission to delete messages in this channel.", 
                ephemeral=True
            )
        except discord.HTTPException as e:
            await interaction.followup.send(
                f"An error occurred while deleting messages: {str(e)}", 
                ephemeral=True
            )

    @app_commands.command(name="avatar", description="Show a enlarged version of the selected user's profile picture")
    @app_commands.describe(user="The user whose avatar you want to see")
    async def avatar(self, interaction: discord.Interaction, user: discord.Member):
        """Display a user's avatar"""
        embed = discord.Embed(
            title=f"Profile Picture | {user.display_name}",
            color=0x566374
        )
        
        embed.set_image(url=user.display_avatar.url)
        
        await interaction.response.send_message(embed=embed)

async def setup(bot):
    await bot.add_cog(GeneralCommands(bot))

















