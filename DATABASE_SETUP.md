# PostgreSQL Database Setup for Railway.com

## ✅ Setup Complete!

Your Discord bot has been successfully configured to use PostgreSQL on Railway.com instead of JSON files.

## What Was Done

### 1. Database Module Created (`cogs/db.py`)
- Created a complete database manager with connection pooling
- Hardcoded Railway connection details for security
- Automatic table creation for all your JSON data types:
  - `confessions` - Stores confession data
  - `counting` - Stores counting game data  
  - `role_messages` - Stores role message data
  - `user_forms` - Stores user form data
  - `vc_timeouts` - Stores voice channel timeout data
  - `warnings` - Stores warning data

### 2. Database Persistence Class (`cogs/utils.py`)
- Added `DatabasePersistence` class with methods:
  - `load_data(data_type, default_data)` - Load data from PostgreSQL
  - `save_data(data_type, data)` - Save data to PostgreSQL
- Supports all your data types: confessions, counting, role_messages, user_forms, vc_timeouts, warnings

### 3. Dependencies Updated
- Updated `requirements.txt` with `asyncpg>=0.28.0` for PostgreSQL support

### 4. Connection Details (Already Configured)
```
Host: postgres.railway.internal
Port: 5432
Database: railway
Username: postgres
Password: SWvRuBGMfUKzPlYgrbGmEDNSMRffOfnZ
```

## Database Tables Created

When your bot starts, it will automatically create these tables:

1. **confessions**
   - `active_confessions` (JSONB)
   - `last_id` (INTEGER)
   - `message_ids` (JSONB)
   - `current_id` (INTEGER)
   - `active_votes` (JSONB)

2. **counting**
   - `current_number` (INTEGER)
   - `highest_number` (INTEGER)
   - `last_counter` (TEXT)
   - `counter_stats` (JSONB)
   - `failure_stats` (JSONB)
   - `current_streak` (INTEGER)
   - `current_streak_user` (TEXT)
   - `highest_streaks` (JSONB)
   - `saves` (JSONB)

3. **role_messages**
   - `data` (JSONB)

4. **user_forms**
   - `data` (JSONB)

5. **vc_timeouts**
   - `data` (JSONB)

6. **warnings**
   - `warnings` (JSONB)

All tables include:
- `id` (Primary Key)
- `updated_at` (Timestamp)

## How to Use

### For Existing Cogs
Your cogs can now use the database instead of JSON files:

```python
from .db import db
from .utils import DatabasePersistence

# Load data
data, status = await DatabasePersistence.load_data('confessions', default_data={})

# Save data
success, status = await DatabasePersistence.save_data('confessions', data)
```

### Direct Database Access
```python
from .db import db

# Get confessions data
confessions = await db.get_confessions_data()

# Save confessions data
await db.save_confessions_data(confessions)
```

## Next Steps

1. **Deploy to Railway**: The database will only work when your bot is deployed to Railway (not locally)

2. **Update Your Cogs**: You can gradually update your cogs to use `DatabasePersistence` instead of `JsonPersistence`

3. **Data Migration**: Since you chose to start fresh, all tables will be initialized with empty data

4. **Test**: Once deployed, your bot will automatically:
   - Connect to the PostgreSQL database
   - Create all necessary tables
   - Initialize them with default data
   - Start using PostgreSQL for persistence

## Files Modified/Created

- ✅ `cogs/db.py` - New database manager
- ✅ `cogs/utils.py` - Added DatabasePersistence class
- ✅ `requirements.txt` - Added asyncpg dependency
- ✅ `test_db.py` - Test script (for Railway deployment only)
- ✅ `DATABASE_SETUP.md` - This documentation

## Important Notes

- The database connection will only work when deployed to Railway
- All your JSON data structures are preserved in JSONB format
- The bot will automatically handle database initialization
- Your existing JSON files in the `data/` folder are preserved as backup

Your bot is now ready to use PostgreSQL on Railway.com! 🚀