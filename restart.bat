@echo off
echo Restarting bot in 3 seconds...
timeout /t 3 /nobreak >nul
cd /d "%~dp0"

REM Try different Python commands in order of preference
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Using python command...
    python main.py
    goto :end
)

py --version >nul 2>&1
if %errorlevel% == 0 (
    echo Using py command...
    py main.py
    goto :end
)

python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo Using python3 command...
    python3 main.py
    goto :end
)

echo ERROR: No Python installation found!
pause

:end