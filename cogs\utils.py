# Responsible for handling backups and persistence

import json
import os
import shutil
from datetime import datetime
from discord.ext import commands
from .db import db

class JsonPersistence:
    @staticmethod
    def get_data_directory():
        # Get data directory path set by main.py (Railway persistent storage or local fallback)
        data_dir = os.getenv('BOT_DATA_DIR', 'data')
        os.makedirs(data_dir, exist_ok=True)
        return data_dir

    @staticmethod
    def load_json_file(file_path, default_data=None):
        """
        Load JSON file with automatic backup restoration if needed
        Returns: (data, status_message)
        """
        if default_data is None:
            default_data = {}
            
        # Modify the file path to use Railway's persistent storage
        persistent_path = os.path.join(JsonPersistence.get_data_directory(), 
                                     os.path.basename(file_path))
        
        backup_path = f"{persistent_path}.backup"
        temp_path = f"{persistent_path}.tmp"
        
        # Clean up any leftover temp files
        if os.path.exists(temp_path):
            os.remove(temp_path)

        # If file doesn't exist, create it with default data
        if not os.path.exists(persistent_path):
            JsonPersistence.save_json_file(persistent_path, default_data)
            return default_data, "Created new file with default data"

        # Try loading the main file first
        try:
            if os.path.exists(persistent_path):
                with open(persistent_path, 'r') as f:
                    return json.load(f), "Main file loaded successfully"
        except json.JSONDecodeError as e:
            print(f"Main file corrupted: {persistent_path}")
            
            # Try loading the backup file
            try:
                if os.path.exists(backup_path):
                    with open(backup_path, 'r') as f:
                        data = json.load(f)
                        
                    # Backup is valid, restore it
                    archive_path = f"{persistent_path}.corrupted_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    os.rename(persistent_path, archive_path)
                    shutil.copy2(backup_path, persistent_path)
                    return data, f"Restored from backup. Corrupted file archived as {os.path.basename(archive_path)}"
            except (json.JSONDecodeError, FileNotFoundError) as backup_error:
                print(f"Backup file also corrupted or missing: {backup_error}")
            
            # If we get here, both main and backup files are corrupted/missing
            JsonPersistence.save_json_file(persistent_path, default_data)
            return default_data, "Created new file with default data after failed recovery"
            
        except Exception as e:
            print(f"Unexpected error loading {persistent_path}: {e}")
            return default_data, f"Error loading file: {str(e)}"

    @staticmethod
    def save_json_file(file_path, data):
        """Save JSON file with backup creation"""
        # Modify the file path to use Railway's persistent storage
        persistent_path = os.path.join(JsonPersistence.get_data_directory(), 
                                     os.path.basename(file_path))
        
        temp_path = f"{persistent_path}.tmp"
        backup_path = f"{persistent_path}.backup"
        
        try:
            # Write to temporary file first
            with open(temp_path, 'w') as f:
                json.dump(data, f, indent=4)
                
            # Create backup of current file if it exists
            if os.path.exists(persistent_path):
                shutil.copy2(persistent_path, backup_path)
                
            # Replace the main file with our new data
            os.replace(temp_path, persistent_path)
            return True, "File saved successfully"
            
        except Exception as e:
            if os.path.exists(temp_path):
                os.remove(temp_path)
            return False, f"Error saving file: {str(e)}"

class DatabasePersistence:
    """New database-based persistence system to replace JSON files"""
    
    @staticmethod
    async def load_data(data_type: str, default_data=None):
        """
        Load data from PostgreSQL database
        
        Args:
            data_type: Type of data to load ('confessions', 'counting', 'role_messages', 
                      'user_forms', 'vc_timeouts', 'warnings')
            default_data: Default data to return if none exists
        
        Returns:
            (data, status_message)
        """
        if default_data is None:
            default_data = {}
        
        try:
            if not db.connected:
                return default_data, "Database not connected, using default data"
            
            if data_type == 'confessions':
                data = await db.get_confessions_data()
            elif data_type == 'counting':
                data = await db.get_counting_data()
            elif data_type == 'role_messages':
                data = await db.get_role_messages_data()
            elif data_type == 'user_forms':
                data = await db.get_user_forms_data()
            elif data_type == 'vc_timeouts':
                data = await db.get_vc_timeouts_data()
            elif data_type == 'warnings':
                data = await db.get_warnings_data()
            else:
                return default_data, f"Unknown data type: {data_type}"
            
            return data, f"Loaded {data_type} data from database"
            
        except Exception as e:
            print(f"Error loading {data_type} data from database: {e}")
            return default_data, f"Error loading from database: {str(e)}"
    
    @staticmethod
    async def save_data(data_type: str, data):
        """
        Save data to PostgreSQL database
        
        Args:
            data_type: Type of data to save ('confessions', 'counting', 'role_messages', 
                      'user_forms', 'vc_timeouts', 'warnings')
            data: Data to save
        
        Returns:
            (success, status_message)
        """
        try:
            if not db.connected:
                return False, "Database not connected"
            
            if data_type == 'confessions':
                await db.save_confessions_data(data)
            elif data_type == 'counting':
                await db.save_counting_data(data)
            elif data_type == 'role_messages':
                await db.save_role_messages_data(data)
            elif data_type == 'user_forms':
                await db.save_user_forms_data(data)
            elif data_type == 'vc_timeouts':
                await db.save_vc_timeouts_data(data)
            elif data_type == 'warnings':
                await db.save_warnings_data(data)
            else:
                return False, f"Unknown data type: {data_type}"
            
            return True, f"Saved {data_type} data to database"
            
        except Exception as e:
            print(f"Error saving {data_type} data to database: {e}")
            return False, f"Error saving to database: {str(e)}"

class Utils(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

async def setup(bot):
    await bot.add_cog(Utils(bot))




